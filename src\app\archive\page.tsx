'use client';

import React, { useEffect, useState } from 'react';
import Link from 'next/link';
import { Navbar } from '@/components/layout/Navbar';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { CorrespondenceStorage } from '@/lib/data';
import { Correspondence } from '@/types/correspondence';
import { 
  Archive, 
  Search, 
  RotateCcw, 
  Eye, 
  Calendar,
  User,
  Building,
  AlertCircle,
  Mail,
  Send,
  Filter
} from 'lucide-react';
import { format } from 'date-fns';
import { ar } from 'date-fns/locale';

export default function ArchivePage() {
  const [archivedCorrespondences, setArchivedCorrespondences] = useState<Correspondence[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredCorrespondences, setFilteredCorrespondences] = useState<Correspondence[]>([]);
  const [selectedType, setSelectedType] = useState<'all' | 'واردة' | 'صادرة'>('all');

  useEffect(() => {
    loadArchivedCorrespondences();
  }, []);

  useEffect(() => {
    filterCorrespondences();
  }, [searchTerm, selectedType, archivedCorrespondences]);

  const loadArchivedCorrespondences = () => {
    const all = CorrespondenceStorage.getAllCorrespondences();
    const archived = all.filter(c => c.status === 'مؤرشفة');
    setArchivedCorrespondences(archived);
  };

  const filterCorrespondences = () => {
    let filtered = archivedCorrespondences;

    // فلترة حسب النوع
    if (selectedType !== 'all') {
      filtered = filtered.filter(c => c.type === selectedType);
    }

    // فلترة حسب النص
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(c => 
        c.subject.toLowerCase().includes(term) ||
        c.content.toLowerCase().includes(term) ||
        c.referenceNumber.toLowerCase().includes(term) ||
        (c.type === 'واردة' && (c as any).senderName.toLowerCase().includes(term)) ||
        (c.type === 'صادرة' && (c as any).recipientName.toLowerCase().includes(term))
      );
    }

    setFilteredCorrespondences(filtered);
  };

  const handleRestore = (correspondence: Correspondence) => {
    if (confirm('هل أنت متأكد من استعادة هذه المراسلة من الأرشيف؟')) {
      const updatedCorrespondence = {
        ...correspondence,
        status: 'مكتملة' as any,
        updatedAt: new Date()
      };

      if (correspondence.type === 'واردة') {
        CorrespondenceStorage.saveIncomingCorrespondence(updatedCorrespondence as any);
      } else {
        CorrespondenceStorage.saveOutgoingCorrespondence(updatedCorrespondence as any);
      }

      loadArchivedCorrespondences();
    }
  };

  const getStatusColor = (status: string) => {
    return 'bg-gray-100 text-gray-800'; // جميع المراسلات المؤرشفة لها نفس اللون
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'عاجلة جداً': return 'bg-red-100 text-red-800';
      case 'عاجلة': return 'bg-orange-100 text-orange-800';
      case 'مهمة': return 'bg-yellow-100 text-yellow-800';
      case 'عادية': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getArchiveStats = () => {
    const total = archivedCorrespondences.length;
    const incoming = archivedCorrespondences.filter(c => c.type === 'واردة').length;
    const outgoing = archivedCorrespondences.filter(c => c.type === 'صادرة').length;
    
    return { total, incoming, outgoing };
  };

  const stats = getArchiveStats();

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {/* Header */}
          <div className="mb-6">
            <h1 className="text-3xl font-bold text-gray-900">أرشيف المراسلات</h1>
            <p className="text-gray-600 mt-1">
              إدارة واستعادة المراسلات المؤرشفة
            </p>
          </div>

          {/* Statistics */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <Archive className="h-8 w-8 text-gray-600" />
                  </div>
                  <div className="mr-4">
                    <div className="text-2xl font-bold text-gray-900">
                      {stats.total}
                    </div>
                    <div className="text-sm text-gray-600">إجمالي المؤرشف</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <Mail className="h-8 w-8 text-blue-600" />
                  </div>
                  <div className="mr-4">
                    <div className="text-2xl font-bold text-gray-900">
                      {stats.incoming}
                    </div>
                    <div className="text-sm text-gray-600">واردة مؤرشفة</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <Send className="h-8 w-8 text-green-600" />
                  </div>
                  <div className="mr-4">
                    <div className="text-2xl font-bold text-gray-900">
                      {stats.outgoing}
                    </div>
                    <div className="text-sm text-gray-600">صادرة مؤرشفة</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Search and Filters */}
          <Card className="mb-6">
            <CardContent className="p-6">
              <div className="flex flex-col md:flex-row gap-4">
                <div className="flex-1">
                  <Input
                    placeholder="البحث في المراسلات المؤرشفة..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full"
                  />
                </div>
                <div className="flex gap-2">
                  <select
                    value={selectedType}
                    onChange={(e) => setSelectedType(e.target.value as any)}
                    className="rounded-md border border-gray-300 px-3 py-2"
                  >
                    <option value="all">جميع الأنواع</option>
                    <option value="واردة">واردة</option>
                    <option value="صادرة">صادرة</option>
                  </select>
                  <Button variant="outline">
                    <Filter className="h-4 w-4 ml-2" />
                    فلاتر
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Archived Correspondences List */}
          {filteredCorrespondences.length === 0 ? (
            <Card>
              <CardContent className="p-12 text-center">
                <Archive className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  {archivedCorrespondences.length === 0 
                    ? 'لا توجد مراسلات مؤرشفة'
                    : 'لا توجد نتائج تطابق البحث'
                  }
                </h3>
                <p className="text-gray-600">
                  {archivedCorrespondences.length === 0 
                    ? 'لم يتم أرشفة أي مراسلات بعد'
                    : 'جرب تغيير معايير البحث أو الفلترة'
                  }
                </p>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-4">
              {filteredCorrespondences.map((correspondence) => (
                <Card key={correspondence.id}>
                  <CardContent className="p-6">
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-3">
                          <div className="flex items-center">
                            {correspondence.type === 'واردة' ? (
                              <Mail className="h-5 w-5 text-blue-600 ml-2" />
                            ) : (
                              <Send className="h-5 w-5 text-green-600 ml-2" />
                            )}
                            <span className="text-sm font-medium text-gray-600">
                              {correspondence.type}
                            </span>
                          </div>
                          <h3 className="text-lg font-semibold text-gray-900">
                            {correspondence.subject}
                          </h3>
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(correspondence.status)}`}>
                            مؤرشفة
                          </span>
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(correspondence.priority)}`}>
                            {correspondence.priority}
                          </span>
                        </div>
                        
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600 mb-4">
                          <div className="flex items-center">
                            <User className="h-4 w-4 ml-2" />
                            <span>
                              {correspondence.type === 'واردة' 
                                ? (correspondence as any).senderName 
                                : (correspondence as any).recipientName}
                            </span>
                          </div>
                          <div className="flex items-center">
                            <Building className="h-4 w-4 ml-2" />
                            <span>
                              {correspondence.type === 'واردة' 
                                ? (correspondence as any).senderOrganization 
                                : (correspondence as any).recipientOrganization}
                            </span>
                          </div>
                          <div className="flex items-center">
                            <Calendar className="h-4 w-4 ml-2" />
                            <span>
                              {format(correspondence.updatedAt, 'dd/MM/yyyy', { locale: ar })}
                            </span>
                          </div>
                        </div>
                        
                        <p className="text-gray-700 mb-3 line-clamp-2">
                          {correspondence.content}
                        </p>
                        
                        <div className="flex items-center gap-4 text-xs text-gray-500">
                          <span>رقم المرجع: {correspondence.referenceNumber}</span>
                          {correspondence.attachments.length > 0 && (
                            <span>{correspondence.attachments.length} مرفق</span>
                          )}
                          <span>
                            تاريخ الأرشفة: {format(correspondence.updatedAt, 'dd/MM/yyyy', { locale: ar })}
                          </span>
                        </div>
                      </div>
                      
                      <div className="flex gap-2 mr-4">
                        <Link href={`/${correspondence.type === 'واردة' ? 'incoming' : 'outgoing'}/${correspondence.id}`}>
                          <Button variant="outline" size="sm">
                            <Eye className="h-4 w-4" />
                          </Button>
                        </Link>
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => handleRestore(correspondence)}
                          className="text-blue-600 hover:text-blue-700"
                        >
                          <RotateCcw className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}

          {/* Archive Info */}
          {archivedCorrespondences.length > 0 && (
            <Card className="mt-6">
              <CardContent className="p-6">
                <div className="flex items-center text-sm text-gray-600">
                  <AlertCircle className="h-4 w-4 ml-2" />
                  <span>
                    المراسلات المؤرشفة محفوظة بشكل دائم ويمكن استعادتها في أي وقت. 
                    استخدم زر الاستعادة لإرجاع المراسلة إلى حالة "مكتملة".
                  </span>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </main>
    </div>
  );
}
