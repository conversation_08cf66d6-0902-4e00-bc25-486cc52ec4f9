'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { Navbar } from '@/components/layout/Navbar';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Input, Select } from '@/components/ui/Input';
import { CorrespondenceStorage } from '@/lib/data';
import { Correspondence, CorrespondenceFilter } from '@/types/correspondence';
import { 
  Search, 
  Filter, 
  X, 
  Eye, 
  Calendar,
  User,
  Building,
  FileText,
  Mail,
  Send
} from 'lucide-react';
import { format } from 'date-fns';
import { ar } from 'date-fns/locale';

export default function SearchPage() {
  const [results, setResults] = useState<Correspondence[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [showAdvanced, setShowAdvanced] = useState(false);
  
  const [filters, setFilters] = useState<CorrespondenceFilter>({
    searchTerm: '',
    type: undefined,
    status: undefined,
    priority: undefined,
    dateFrom: undefined,
    dateTo: undefined,
    senderOrganization: '',
    recipientOrganization: ''
  });

  const typeOptions = [
    { value: 'واردة', label: 'واردة' },
    { value: 'صادرة', label: 'صادرة' }
  ];

  const statusOptions = [
    { value: 'جديدة', label: 'جديدة' },
    { value: 'قيد المراجعة', label: 'قيد المراجعة' },
    { value: 'مكتملة', label: 'مكتملة' },
    { value: 'مؤرشفة', label: 'مؤرشفة' },
    { value: 'ملغاة', label: 'ملغاة' }
  ];

  const priorityOptions = [
    { value: 'عادية', label: 'عادية' },
    { value: 'مهمة', label: 'مهمة' },
    { value: 'عاجلة', label: 'عاجلة' },
    { value: 'عاجلة جداً', label: 'عاجلة جداً' }
  ];

  const handleFilterChange = (field: keyof CorrespondenceFilter, value: any) => {
    setFilters(prev => ({
      ...prev,
      [field]: value || undefined
    }));
  };

  const handleSearch = () => {
    setIsSearching(true);
    try {
      const searchFilters: CorrespondenceFilter = {
        ...filters,
        dateFrom: filters.dateFrom ? new Date(filters.dateFrom as any) : undefined,
        dateTo: filters.dateTo ? new Date(filters.dateTo as any) : undefined
      };
      
      const searchResults = CorrespondenceStorage.filterCorrespondences(searchFilters);
      setResults(searchResults);
    } catch (error) {
      console.error('Search error:', error);
    } finally {
      setIsSearching(false);
    }
  };

  const handleClearFilters = () => {
    setFilters({
      searchTerm: '',
      type: undefined,
      status: undefined,
      priority: undefined,
      dateFrom: undefined,
      dateTo: undefined,
      senderOrganization: '',
      recipientOrganization: ''
    });
    setResults([]);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'جديدة': return 'bg-blue-100 text-blue-800';
      case 'قيد المراجعة': return 'bg-yellow-100 text-yellow-800';
      case 'مكتملة': return 'bg-green-100 text-green-800';
      case 'مؤرشفة': return 'bg-gray-100 text-gray-800';
      case 'ملغاة': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'عاجلة جداً': return 'bg-red-100 text-red-800';
      case 'عاجلة': return 'bg-orange-100 text-orange-800';
      case 'مهمة': return 'bg-yellow-100 text-yellow-800';
      case 'عادية': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  // البحث التلقائي عند تغيير النص
  useEffect(() => {
    if (filters.searchTerm && filters.searchTerm.length > 2) {
      const timeoutId = setTimeout(() => {
        handleSearch();
      }, 500);
      return () => clearTimeout(timeoutId);
    }
  }, [filters.searchTerm]);

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {/* Header */}
          <div className="mb-6">
            <h1 className="text-3xl font-bold text-gray-900">البحث في المراسلات</h1>
            <p className="text-gray-600 mt-1">
              البحث والفلترة المتقدمة في جميع المراسلات
            </p>
          </div>

          {/* Search Form */}
          <Card className="mb-6">
            <CardHeader>
              <div className="flex justify-between items-center">
                <CardTitle>البحث والفلترة</CardTitle>
                <Button
                  variant="outline"
                  onClick={() => setShowAdvanced(!showAdvanced)}
                >
                  <Filter className="h-4 w-4 ml-2" />
                  {showAdvanced ? 'إخفاء الفلاتر' : 'فلاتر متقدمة'}
                </Button>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Basic Search */}
              <div className="flex gap-4">
                <div className="flex-1">
                  <Input
                    placeholder="البحث في الموضوع، المحتوى، أو رقم المرجع..."
                    value={filters.searchTerm || ''}
                    onChange={(e) => handleFilterChange('searchTerm', e.target.value)}
                  />
                </div>
                <Button onClick={handleSearch} disabled={isSearching}>
                  <Search className="h-4 w-4 ml-2" />
                  {isSearching ? 'جاري البحث...' : 'بحث'}
                </Button>
              </div>

              {/* Advanced Filters */}
              {showAdvanced && (
                <div className="border-t pt-4 space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <Select
                      label="نوع المراسلة"
                      value={filters.type || ''}
                      onChange={(e) => handleFilterChange('type', e.target.value)}
                      options={typeOptions}
                    />
                    
                    <Select
                      label="الحالة"
                      value={filters.status || ''}
                      onChange={(e) => handleFilterChange('status', e.target.value)}
                      options={statusOptions}
                    />
                    
                    <Select
                      label="الأولوية"
                      value={filters.priority || ''}
                      onChange={(e) => handleFilterChange('priority', e.target.value)}
                      options={priorityOptions}
                    />
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <Input
                      label="من تاريخ"
                      type="date"
                      value={filters.dateFrom ? (filters.dateFrom as any) : ''}
                      onChange={(e) => handleFilterChange('dateFrom', e.target.value)}
                    />
                    
                    <Input
                      label="إلى تاريخ"
                      type="date"
                      value={filters.dateTo ? (filters.dateTo as any) : ''}
                      onChange={(e) => handleFilterChange('dateTo', e.target.value)}
                    />
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <Input
                      label="الجهة المرسلة"
                      value={filters.senderOrganization || ''}
                      onChange={(e) => handleFilterChange('senderOrganization', e.target.value)}
                      placeholder="اسم الجهة المرسلة"
                    />
                    
                    <Input
                      label="الجهة المستقبلة"
                      value={filters.recipientOrganization || ''}
                      onChange={(e) => handleFilterChange('recipientOrganization', e.target.value)}
                      placeholder="اسم الجهة المستقبلة"
                    />
                  </div>
                  
                  <div className="flex gap-4">
                    <Button onClick={handleSearch} disabled={isSearching}>
                      <Search className="h-4 w-4 ml-2" />
                      تطبيق الفلاتر
                    </Button>
                    <Button variant="outline" onClick={handleClearFilters}>
                      <X className="h-4 w-4 ml-2" />
                      مسح الفلاتر
                    </Button>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Search Results */}
          {results.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>
                  نتائج البحث ({results.length} نتيجة)
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {results.map((correspondence) => (
                    <div key={correspondence.id} className="border rounded-lg p-4 hover:shadow-md transition-shadow">
                      <div className="flex justify-between items-start">
                        <div className="flex-1">
                          <div className="flex items-center gap-3 mb-3">
                            <div className="flex items-center">
                              {correspondence.type === 'واردة' ? (
                                <Mail className="h-5 w-5 text-blue-600 ml-2" />
                              ) : (
                                <Send className="h-5 w-5 text-green-600 ml-2" />
                              )}
                              <span className="text-sm font-medium text-gray-600">
                                {correspondence.type}
                              </span>
                            </div>
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(correspondence.status)}`}>
                              {correspondence.status}
                            </span>
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(correspondence.priority)}`}>
                              {correspondence.priority}
                            </span>
                          </div>
                          
                          <h3 className="text-lg font-semibold text-gray-900 mb-2">
                            {correspondence.subject}
                          </h3>
                          
                          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600 mb-3">
                            <div className="flex items-center">
                              <User className="h-4 w-4 ml-2" />
                              <span>
                                {correspondence.type === 'واردة' 
                                  ? (correspondence as any).senderName 
                                  : (correspondence as any).recipientName}
                              </span>
                            </div>
                            <div className="flex items-center">
                              <Building className="h-4 w-4 ml-2" />
                              <span>
                                {correspondence.type === 'واردة' 
                                  ? (correspondence as any).senderOrganization 
                                  : (correspondence as any).recipientOrganization}
                              </span>
                            </div>
                            <div className="flex items-center">
                              <Calendar className="h-4 w-4 ml-2" />
                              <span>
                                {format(correspondence.createdAt, 'dd/MM/yyyy', { locale: ar })}
                              </span>
                            </div>
                          </div>
                          
                          <p className="text-gray-700 mb-3 line-clamp-2">
                            {correspondence.content}
                          </p>
                          
                          <div className="flex items-center gap-4 text-xs text-gray-500">
                            <span>رقم المرجع: {correspondence.referenceNumber}</span>
                            {correspondence.attachments.length > 0 && (
                              <span>{correspondence.attachments.length} مرفق</span>
                            )}
                          </div>
                        </div>
                        
                        <div className="mr-4">
                          <Link href={`/${correspondence.type === 'واردة' ? 'incoming' : 'outgoing'}/${correspondence.id}`}>
                            <Button variant="outline" size="sm">
                              <Eye className="h-4 w-4 ml-2" />
                              عرض
                            </Button>
                          </Link>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* No Results */}
          {results.length === 0 && filters.searchTerm && (
            <Card>
              <CardContent className="p-12 text-center">
                <FileText className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  لا توجد نتائج
                </h3>
                <p className="text-gray-600">
                  لم يتم العثور على مراسلات تطابق معايير البحث
                </p>
              </CardContent>
            </Card>
          )}
        </div>
      </main>
    </div>
  );
}
