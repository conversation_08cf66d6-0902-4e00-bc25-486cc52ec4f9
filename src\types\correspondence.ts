// أنواع البيانات الأساسية لنظام المراسلات

export type CorrespondenceStatus = 
  | 'جديدة' 
  | 'قيد المراجعة' 
  | 'مكتملة' 
  | 'مؤرشفة' 
  | 'ملغاة';

export type Priority = 'عادية' | 'مهمة' | 'عاجلة' | 'عاجلة جداً';

export type CorrespondenceType = 'واردة' | 'صادرة';

// نموذج المرفقات
export interface Attachment {
  id: string;
  fileName: string;
  fileSize: number;
  fileType: string;
  uploadDate: Date;
  filePath: string;
}

// نموذج المراسلة الأساسي
export interface BaseCorrespondence {
  id: string;
  referenceNumber: string;
  subject: string;
  content: string;
  priority: Priority;
  status: CorrespondenceStatus;
  createdAt: Date;
  updatedAt: Date;
  attachments: Attachment[];
  notes: string;
  tags: string[];
}

// نموذج المراسلة الواردة
export interface IncomingCorrespondence extends BaseCorrespondence {
  type: 'واردة';
  senderName: string;
  senderOrganization: string;
  senderEmail?: string;
  senderPhone?: string;
  receivedDate: Date;
  receivedBy: string;
  responseRequired: boolean;
  responseDeadline?: Date;
  responseStatus?: 'لم يتم الرد' | 'تم الرد' | 'قيد الإعداد';
}

// نموذج المراسلة الصادرة
export interface OutgoingCorrespondence extends BaseCorrespondence {
  type: 'صادرة';
  recipientName: string;
  recipientOrganization: string;
  recipientEmail?: string;
  recipientPhone?: string;
  sentDate?: Date;
  sentBy: string;
  deliveryMethod: 'بريد إلكتروني' | 'فاكس' | 'بريد عادي' | 'تسليم يدوي' | 'أخرى';
  deliveryStatus: 'قيد الإعداد' | 'تم الإرسال' | 'تم التسليم' | 'فشل الإرسال';
  deliveryConfirmation?: Date;
  inResponseTo?: string; // معرف المراسلة التي يتم الرد عليها
}

// نموذج موحد للمراسلات
export type Correspondence = IncomingCorrespondence | OutgoingCorrespondence;

// نموذج الفلترة والبحث
export interface CorrespondenceFilter {
  type?: CorrespondenceType;
  status?: CorrespondenceStatus;
  priority?: Priority;
  dateFrom?: Date;
  dateTo?: Date;
  searchTerm?: string;
  tags?: string[];
  senderOrganization?: string;
  recipientOrganization?: string;
}

// نموذج الإحصائيات
export interface CorrespondenceStats {
  total: number;
  byStatus: Record<CorrespondenceStatus, number>;
  byPriority: Record<Priority, number>;
  byType: Record<CorrespondenceType, number>;
  monthlyStats: {
    month: string;
    incoming: number;
    outgoing: number;
  }[];
}
