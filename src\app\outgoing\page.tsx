'use client';

import React, { useEffect, useState } from 'react';
import Link from 'next/link';
import { Navbar } from '@/components/layout/Navbar';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { CorrespondenceStorage } from '@/lib/data';
import { OutgoingCorrespondence } from '@/types/correspondence';
import { 
  Plus, 
  Search, 
  Eye, 
  Edit, 
  Trash2,
  Calendar,
  User,
  Building,
  AlertCircle,
  Send
} from 'lucide-react';
import { format } from 'date-fns';
import { ar } from 'date-fns/locale';

export default function OutgoingCorrespondencePage() {
  const [correspondences, setCorrespondences] = useState<OutgoingCorrespondence[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredCorrespondences, setFilteredCorrespondences] = useState<OutgoingCorrespondence[]>([]);

  useEffect(() => {
    loadCorrespondences();
  }, []);

  useEffect(() => {
    if (searchTerm) {
      const filtered = correspondences.filter(c => 
        c.subject.toLowerCase().includes(searchTerm.toLowerCase()) ||
        c.recipientName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        c.referenceNumber.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setFilteredCorrespondences(filtered);
    } else {
      setFilteredCorrespondences(correspondences);
    }
  }, [searchTerm, correspondences]);

  const loadCorrespondences = () => {
    const data = CorrespondenceStorage.getOutgoingCorrespondences();
    setCorrespondences(data);
    setFilteredCorrespondences(data);
  };

  const handleDelete = (id: string) => {
    if (confirm('هل أنت متأكد من حذف هذه المراسلة؟')) {
      CorrespondenceStorage.deleteCorrespondence(id, 'صادرة');
      loadCorrespondences();
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'جديدة': return 'bg-blue-100 text-blue-800';
      case 'قيد المراجعة': return 'bg-yellow-100 text-yellow-800';
      case 'مكتملة': return 'bg-green-100 text-green-800';
      case 'مؤرشفة': return 'bg-gray-100 text-gray-800';
      case 'ملغاة': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getDeliveryStatusColor = (status: string) => {
    switch (status) {
      case 'قيد الإعداد': return 'bg-blue-100 text-blue-800';
      case 'تم الإرسال': return 'bg-yellow-100 text-yellow-800';
      case 'تم التسليم': return 'bg-green-100 text-green-800';
      case 'فشل الإرسال': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'عاجلة جداً': return 'bg-red-100 text-red-800';
      case 'عاجلة': return 'bg-orange-100 text-orange-800';
      case 'مهمة': return 'bg-yellow-100 text-yellow-800';
      case 'عادية': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {/* Header */}
          <div className="flex justify-between items-center mb-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">المراسلات الصادرة</h1>
              <p className="text-gray-600 mt-1">
                إدارة وتتبع جميع المراسلات الصادرة
              </p>
            </div>
            <Link href="/outgoing/new">
              <Button>
                <Plus className="h-4 w-4 ml-2" />
                مراسلة جديدة
              </Button>
            </Link>
          </div>

          {/* Search and Filters */}
          <Card className="mb-6">
            <CardContent className="p-6">
              <div className="flex gap-4">
                <div className="flex-1">
                  <Input
                    placeholder="البحث في المراسلات..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full"
                  />
                </div>
                <Button variant="outline">
                  <Search className="h-4 w-4 ml-2" />
                  بحث متقدم
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Correspondences List */}
          {filteredCorrespondences.length === 0 ? (
            <Card>
              <CardContent className="p-12 text-center">
                <AlertCircle className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  لا توجد مراسلات صادرة
                </h3>
                <p className="text-gray-600 mb-4">
                  ابدأ بإضافة أول مراسلة صادرة
                </p>
                <Link href="/outgoing/new">
                  <Button>
                    <Plus className="h-4 w-4 ml-2" />
                    إضافة مراسلة جديدة
                  </Button>
                </Link>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-4">
              {filteredCorrespondences.map((correspondence) => (
                <Card key={correspondence.id}>
                  <CardContent className="p-6">
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-3">
                          <h3 className="text-lg font-semibold text-gray-900">
                            {correspondence.subject}
                          </h3>
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(correspondence.status)}`}>
                            {correspondence.status}
                          </span>
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDeliveryStatusColor(correspondence.deliveryStatus)}`}>
                            {correspondence.deliveryStatus}
                          </span>
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(correspondence.priority)}`}>
                            {correspondence.priority}
                          </span>
                        </div>
                        
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600 mb-4">
                          <div className="flex items-center">
                            <User className="h-4 w-4 ml-2" />
                            <span>{correspondence.recipientName}</span>
                          </div>
                          <div className="flex items-center">
                            <Building className="h-4 w-4 ml-2" />
                            <span>{correspondence.recipientOrganization}</span>
                          </div>
                          <div className="flex items-center">
                            <Send className="h-4 w-4 ml-2" />
                            <span>{correspondence.deliveryMethod}</span>
                          </div>
                        </div>
                        
                        <p className="text-gray-700 mb-3 line-clamp-2">
                          {correspondence.content}
                        </p>
                        
                        <div className="flex items-center gap-4 text-xs text-gray-500">
                          <span>رقم المرجع: {correspondence.referenceNumber}</span>
                          {correspondence.sentDate && (
                            <div className="flex items-center">
                              <Calendar className="h-3 w-3 ml-1" />
                              <span>
                                تاريخ الإرسال: {format(correspondence.sentDate, 'dd/MM/yyyy', { locale: ar })}
                              </span>
                            </div>
                          )}
                          {correspondence.attachments.length > 0 && (
                            <span>{correspondence.attachments.length} مرفق</span>
                          )}
                        </div>
                      </div>
                      
                      <div className="flex gap-2 mr-4">
                        <Link href={`/outgoing/${correspondence.id}`}>
                          <Button variant="outline" size="sm">
                            <Eye className="h-4 w-4" />
                          </Button>
                        </Link>
                        <Link href={`/outgoing/${correspondence.id}/edit`}>
                          <Button variant="outline" size="sm">
                            <Edit className="h-4 w-4" />
                          </Button>
                        </Link>
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => handleDelete(correspondence.id)}
                        >
                          <Trash2 className="h-4 w-4 text-red-600" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </div>
      </main>
    </div>
  );
}
