// بيانات تجريبية لاختبار النظام
import { IncomingCorrespondence, OutgoingCorrespondence } from '@/types/correspondence';
import { CorrespondenceStorage } from './data';

export const createSampleData = () => {
  // مراسلات واردة تجريبية
  const sampleIncoming: Omit<IncomingCorrespondence, 'id' | 'referenceNumber' | 'createdAt' | 'updatedAt'>[] = [
    {
      type: 'واردة',
      subject: 'طلب معلومات حول الخدمات المتاحة',
      content: 'نرجو منكم تزويدنا بمعلومات مفصلة حول الخدمات المتاحة لديكم والرسوم المترتبة عليها.',
      senderName: 'أحمد محمد علي',
      senderOrganization: 'شركة التطوير التقني',
      senderEmail: '<EMAIL>',
      senderPhone: '0501234567',
      priority: 'مهمة',
      status: 'جديدة',
      receivedDate: new Date('2024-01-15'),
      receivedBy: 'موظف الاستقبال',
      responseRequired: true,
      responseDeadline: new Date('2024-01-25'),
      responseStatus: 'لم يتم الرد',
      attachments: [],
      notes: 'طلب مهم من عميل جديد',
      tags: ['عميل جديد', 'استفسار', 'خدمات']
    },
    {
      type: 'واردة',
      subject: 'شكوى حول جودة الخدمة',
      content: 'نود أن نعبر عن عدم رضانا عن مستوى الخدمة المقدمة ونطلب تحسينها.',
      senderName: 'فاطمة أحمد',
      senderOrganization: 'مؤسسة الخدمات العامة',
      senderEmail: '<EMAIL>',
      priority: 'عاجلة',
      status: 'قيد المراجعة',
      receivedDate: new Date('2024-01-10'),
      receivedBy: 'مدير خدمة العملاء',
      responseRequired: true,
      responseDeadline: new Date('2024-01-20'),
      responseStatus: 'قيد الإعداد',
      attachments: [],
      notes: 'شكوى تتطلب متابعة فورية',
      tags: ['شكوى', 'جودة', 'متابعة']
    },
    {
      type: 'واردة',
      subject: 'دعوة لحضور مؤتمر التقنية',
      content: 'يسرنا دعوتكم لحضور مؤتمر التقنية السنوي الذي سيقام في الرياض.',
      senderName: 'سارة خالد',
      senderOrganization: 'مركز المؤتمرات الدولي',
      senderEmail: '<EMAIL>',
      priority: 'عادية',
      status: 'مكتملة',
      receivedDate: new Date('2024-01-05'),
      receivedBy: 'مساعد المدير',
      responseRequired: false,
      attachments: [],
      notes: 'دعوة رسمية للمؤتمر',
      tags: ['مؤتمر', 'دعوة', 'تقنية']
    }
  ];

  // مراسلات صادرة تجريبية
  const sampleOutgoing: Omit<OutgoingCorrespondence, 'id' | 'referenceNumber' | 'createdAt' | 'updatedAt'>[] = [
    {
      type: 'صادرة',
      subject: 'رد على طلب المعلومات',
      content: 'نشكركم على استفساركم ونرفق لكم المعلومات المطلوبة حول خدماتنا.',
      recipientName: 'أحمد محمد علي',
      recipientOrganization: 'شركة التطوير التقني',
      recipientEmail: '<EMAIL>',
      priority: 'مهمة',
      status: 'مكتملة',
      deliveryMethod: 'بريد إلكتروني',
      deliveryStatus: 'تم التسليم',
      sentDate: new Date('2024-01-20'),
      sentBy: 'موظف خدمة العملاء',
      deliveryConfirmation: new Date('2024-01-20'),
      attachments: [],
      notes: 'تم الرد في الوقت المحدد',
      tags: ['رد', 'معلومات', 'خدمات']
    },
    {
      type: 'صادرة',
      subject: 'إشعار بتحديث الخدمات',
      content: 'نود إعلامكم بأنه تم تحديث خدماتنا وإضافة ميزات جديدة.',
      recipientName: 'محمد سعد',
      recipientOrganization: 'شركة الأعمال المتقدمة',
      recipientEmail: '<EMAIL>',
      priority: 'عادية',
      status: 'تم الإرسال',
      deliveryMethod: 'بريد إلكتروني',
      deliveryStatus: 'تم الإرسال',
      sentDate: new Date('2024-01-18'),
      sentBy: 'مدير التسويق',
      attachments: [],
      notes: 'إشعار دوري للعملاء',
      tags: ['إشعار', 'تحديث', 'خدمات']
    },
    {
      type: 'صادرة',
      subject: 'دعوة لاجتماع الشراكة',
      content: 'ندعوكم لحضور اجتماع مناقشة فرص الشراكة المستقبلية.',
      recipientName: 'عبدالله أحمد',
      recipientOrganization: 'مجموعة الاستثمار الخليجي',
      recipientEmail: '<EMAIL>',
      priority: 'مهمة',
      status: 'قيد الإعداد',
      deliveryMethod: 'تسليم يدوي',
      deliveryStatus: 'قيد الإعداد',
      sentBy: 'المدير العام',
      attachments: [],
      notes: 'اجتماع مهم للشراكة',
      tags: ['دعوة', 'اجتماع', 'شراكة']
    }
  ];

  // إضافة البيانات التجريبية
  sampleIncoming.forEach(data => {
    const correspondence: IncomingCorrespondence = {
      ...data,
      id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
      referenceNumber: CorrespondenceStorage.getNextReferenceNumber(),
      createdAt: data.receivedDate,
      updatedAt: new Date()
    };
    CorrespondenceStorage.saveIncomingCorrespondence(correspondence);
  });

  sampleOutgoing.forEach(data => {
    const correspondence: OutgoingCorrespondence = {
      ...data,
      id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
      referenceNumber: CorrespondenceStorage.getNextReferenceNumber(),
      createdAt: data.sentDate || new Date(),
      updatedAt: new Date()
    };
    CorrespondenceStorage.saveOutgoingCorrespondence(correspondence);
  });

  console.log('تم إنشاء البيانات التجريبية بنجاح');
};

// دالة لمسح جميع البيانات
export const clearAllData = () => {
  localStorage.removeItem('incoming_correspondence');
  localStorage.removeItem('outgoing_correspondence');
  localStorage.removeItem('correspondence_counter');
  console.log('تم مسح جميع البيانات');
};
