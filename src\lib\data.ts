// مكتبة إدارة البيانات المحلية (Local Storage)
import { Correspondence, IncomingCorrespondence, OutgoingCorrespondence, CorrespondenceFilter } from '@/types/correspondence';

const STORAGE_KEYS = {
  INCOMING: 'incoming_correspondence',
  OUTGOING: 'outgoing_correspondence',
  COUNTER: 'correspondence_counter'
};

// وظائف مساعدة للتخزين المحلي
export class CorrespondenceStorage {
  
  // الحصول على رقم مرجعي جديد
  static getNextReferenceNumber(): string {
    const counter = parseInt(localStorage.getItem(STORAGE_KEYS.COUNTER) || '0') + 1;
    localStorage.setItem(STORAGE_KEYS.COUNTER, counter.toString());
    const year = new Date().getFullYear();
    return `${year}/${counter.toString().padStart(4, '0')}`;
  }

  // حفظ المراسلة الواردة
  static saveIncomingCorrespondence(correspondence: IncomingCorrespondence): void {
    const existing = this.getIncomingCorrespondences();
    const index = existing.findIndex(c => c.id === correspondence.id);
    
    if (index >= 0) {
      existing[index] = correspondence;
    } else {
      existing.push(correspondence);
    }
    
    localStorage.setItem(STORAGE_KEYS.INCOMING, JSON.stringify(existing));
  }

  // حفظ المراسلة الصادرة
  static saveOutgoingCorrespondence(correspondence: OutgoingCorrespondence): void {
    const existing = this.getOutgoingCorrespondences();
    const index = existing.findIndex(c => c.id === correspondence.id);
    
    if (index >= 0) {
      existing[index] = correspondence;
    } else {
      existing.push(correspondence);
    }
    
    localStorage.setItem(STORAGE_KEYS.OUTGOING, JSON.stringify(existing));
  }

  // الحصول على المراسلات الواردة
  static getIncomingCorrespondences(): IncomingCorrespondence[] {
    const data = localStorage.getItem(STORAGE_KEYS.INCOMING);
    if (!data) return [];
    
    return JSON.parse(data).map((item: any) => ({
      ...item,
      createdAt: new Date(item.createdAt),
      updatedAt: new Date(item.updatedAt),
      receivedDate: new Date(item.receivedDate),
      responseDeadline: item.responseDeadline ? new Date(item.responseDeadline) : undefined,
      attachments: item.attachments.map((att: any) => ({
        ...att,
        uploadDate: new Date(att.uploadDate)
      }))
    }));
  }

  // الحصول على المراسلات الصادرة
  static getOutgoingCorrespondences(): OutgoingCorrespondence[] {
    const data = localStorage.getItem(STORAGE_KEYS.OUTGOING);
    if (!data) return [];
    
    return JSON.parse(data).map((item: any) => ({
      ...item,
      createdAt: new Date(item.createdAt),
      updatedAt: new Date(item.updatedAt),
      sentDate: item.sentDate ? new Date(item.sentDate) : undefined,
      deliveryConfirmation: item.deliveryConfirmation ? new Date(item.deliveryConfirmation) : undefined,
      attachments: item.attachments.map((att: any) => ({
        ...att,
        uploadDate: new Date(att.uploadDate)
      }))
    }));
  }

  // الحصول على جميع المراسلات
  static getAllCorrespondences(): Correspondence[] {
    return [
      ...this.getIncomingCorrespondences(),
      ...this.getOutgoingCorrespondences()
    ];
  }

  // البحث والفلترة
  static filterCorrespondences(filter: CorrespondenceFilter): Correspondence[] {
    let correspondences = this.getAllCorrespondences();

    if (filter.type) {
      correspondences = correspondences.filter(c => c.type === filter.type);
    }

    if (filter.status) {
      correspondences = correspondences.filter(c => c.status === filter.status);
    }

    if (filter.priority) {
      correspondences = correspondences.filter(c => c.priority === filter.priority);
    }

    if (filter.searchTerm) {
      const term = filter.searchTerm.toLowerCase();
      correspondences = correspondences.filter(c => 
        c.subject.toLowerCase().includes(term) ||
        c.content.toLowerCase().includes(term) ||
        c.referenceNumber.toLowerCase().includes(term)
      );
    }

    if (filter.dateFrom) {
      correspondences = correspondences.filter(c => c.createdAt >= filter.dateFrom!);
    }

    if (filter.dateTo) {
      correspondences = correspondences.filter(c => c.createdAt <= filter.dateTo!);
    }

    return correspondences.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
  }

  // حذف المراسلة
  static deleteCorrespondence(id: string, type: 'واردة' | 'صادرة'): void {
    if (type === 'واردة') {
      const existing = this.getIncomingCorrespondences();
      const filtered = existing.filter(c => c.id !== id);
      localStorage.setItem(STORAGE_KEYS.INCOMING, JSON.stringify(filtered));
    } else {
      const existing = this.getOutgoingCorrespondences();
      const filtered = existing.filter(c => c.id !== id);
      localStorage.setItem(STORAGE_KEYS.OUTGOING, JSON.stringify(filtered));
    }
  }

  // الحصول على مراسلة بالمعرف
  static getCorrespondenceById(id: string): Correspondence | null {
    const all = this.getAllCorrespondences();
    return all.find(c => c.id === id) || null;
  }
}
