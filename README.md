# نظام إدارة المراسلات

نظام شامل لإدارة المراسلات الواردة والصادرة مطور باستخدام Next.js و TypeScript.

## المميزات الرئيسية

### 📥 إدارة المراسلات الواردة
- تسجيل المراسلات الواردة مع التفاصيل الكاملة
- تتبع حالة المراسلة (جديدة، قيد المراجعة، مكتملة، مؤرشفة)
- إدارة معلومات المرسل والجهة المرسلة
- تحديد الأولوية والموضوع
- تتبع متطلبات الرد والمواعيد النهائية

### 📤 إدارة المراسلات الصادرة
- إنشاء مراسلات صادرة جديدة
- تتبع حالة الإرسال والتسليم
- إدارة معلومات المستقبل والجهة المستقبلة
- تحديد طريقة التسليم
- ربط المراسلات الصادرة بالواردة (الردود)

### 🔍 نظام البحث والفلترة المتقدم
- البحث بالرقم المرجعي والموضوع والمحتوى
- الفلترة حسب النوع والحالة والأولوية
- البحث بالتاريخ والجهات
- فلاتر متقدمة متعددة المعايير

### 📊 التقارير والإحصائيات
- تقارير شاملة للمراسلات
- إحصائيات حسب النوع والحالة والأولوية
- اتجاهات شهرية وسنوية
- إمكانية تصدير التقارير

### 🗄️ نظام الأرشفة
- أرشفة المراسلات المكتملة
- استعادة المراسلات من الأرشيف
- إدارة دورة حياة الوثائق

## التقنيات المستخدمة

- **Frontend**: Next.js 14, React 18, TypeScript
- **Styling**: Tailwind CSS
- **Icons**: Lucide React
- **Forms**: React Hook Form, Zod
- **Date Handling**: date-fns
- **Storage**: Local Storage (يمكن ترقيته لقاعدة بيانات)

## متطلبات التشغيل

- Node.js 18+ 
- npm أو yarn

## التثبيت والتشغيل

1. **استنساخ المشروع**:
   ```bash
   git clone [repository-url]
   cd correspondence-system
   ```

2. **تثبيت التبعيات**:
   ```bash
   npm install
   ```

3. **تشغيل الخادم التطويري**:
   ```bash
   npm run dev
   ```

4. **فتح المتصفح**:
   افتح [http://localhost:3000](http://localhost:3000)

## هيكل المشروع

```
src/
├── app/                    # صفحات Next.js App Router
│   ├── incoming/          # صفحات المراسلات الواردة
│   ├── outgoing/          # صفحات المراسلات الصادرة
│   ├── search/            # صفحة البحث
│   ├── reports/           # صفحة التقارير
│   ├── archive/           # صفحة الأرشيف
│   └── page.tsx           # الصفحة الرئيسية
├── components/            # المكونات القابلة لإعادة الاستخدام
│   ├── ui/               # مكونات واجهة المستخدم
│   └── layout/           # مكونات التخطيط
├── lib/                  # المكتبات والوظائف المساعدة
│   ├── data.ts          # إدارة البيانات
│   └── sampleData.ts    # البيانات التجريبية
└── types/               # تعريفات TypeScript
    └── correspondence.ts # أنواع البيانات
```

## الاستخدام

### البدء السريع

1. **إنشاء بيانات تجريبية**: 
   - اذهب للصفحة الرئيسية
   - اضغط على "إنشاء بيانات تجريبية"

2. **إضافة مراسلة واردة جديدة**:
   - اذهب لقسم "المراسلات الواردة"
   - اضغط "مراسلة جديدة"
   - املأ النموذج واحفظ

3. **إضافة مراسلة صادرة جديدة**:
   - اذهب لقسم "المراسلات الصادرة"  
   - اضغط "مراسلة جديدة"
   - املأ النموذج واحفظ

4. **البحث والفلترة**:
   - استخدم صفحة البحث للعثور على مراسلات محددة
   - استخدم الفلاتر المتقدمة لتضييق النتائج

5. **عرض التقارير**:
   - اذهب لقسم التقارير لعرض الإحصائيات
   - اختر الفترة الزمنية المطلوبة

## المميزات المتقدمة

### نظام الأرقام المرجعية
- يتم إنشاء رقم مرجعي فريد لكل مراسلة
- تنسيق: السنة/الرقم التسلسلي (مثال: 2024/0001)

### إدارة الحالات
- **جديدة**: مراسلة تم إنشاؤها حديثاً
- **قيد المراجعة**: مراسلة قيد المعالجة
- **مكتملة**: مراسلة تم الانتهاء منها
- **مؤرشفة**: مراسلة محفوظة في الأرشيف
- **ملغاة**: مراسلة ملغاة

### مستويات الأولوية
- **عادية**: أولوية منخفضة
- **مهمة**: أولوية متوسطة
- **عاجلة**: أولوية عالية
- **عاجلة جداً**: أولوية قصوى

## التطوير المستقبلي

### تحسينات مقترحة
- [ ] إضافة قاعدة بيانات حقيقية (PostgreSQL/MySQL)
- [ ] نظام المستخدمين والصلاحيات
- [ ] إدارة المرفقات والملفات
- [ ] إشعارات البريد الإلكتروني
- [ ] تصدير التقارير بصيغ مختلفة (PDF, Excel)
- [ ] واجهة برمجة التطبيقات (API)
- [ ] تطبيق الهاتف المحمول
- [ ] التكامل مع أنظمة خارجية

### الأمان
- [ ] تشفير البيانات الحساسة
- [ ] مراجعة الأمان (Security Audit)
- [ ] نسخ احتياطية تلقائية
- [ ] سجل العمليات (Audit Log)

## المساهمة

نرحب بالمساهمات! يرجى:

1. عمل Fork للمشروع
2. إنشاء فرع للميزة الجديدة
3. تطبيق التغييرات مع الاختبارات
4. إرسال Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## الدعم

للحصول على الدعم أو الإبلاغ عن مشاكل:
- افتح Issue في GitHub
- راسلنا على البريد الإلكتروني

---

**ملاحظة**: هذا النظام مطور للاستخدام الداخلي ويستخدم التخزين المحلي. للاستخدام في الإنتاج، يُنصح بإضافة قاعدة بيانات ونظام مصادقة مناسب.
