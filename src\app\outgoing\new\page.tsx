'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Navbar } from '@/components/layout/Navbar';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Input, Textarea, Select } from '@/components/ui/Input';
import { CorrespondenceStorage } from '@/lib/data';
import { OutgoingCorrespondence, Priority, CorrespondenceStatus } from '@/types/correspondence';
import { ArrowRight, Save, X } from 'lucide-react';

export default function NewOutgoingCorrespondencePage() {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState({
    subject: '',
    content: '',
    recipientName: '',
    recipientOrganization: '',
    recipientEmail: '',
    recipientPhone: '',
    priority: 'عادية' as Priority,
    status: 'قيد الإعداد' as CorrespondenceStatus,
    deliveryMethod: 'بريد إلكتروني',
    deliveryStatus: 'قيد الإعداد',
    sentDate: '',
    notes: '',
    tags: '',
    inResponseTo: ''
  });

  const priorityOptions = [
    { value: 'عادية', label: 'عادية' },
    { value: 'مهمة', label: 'مهمة' },
    { value: 'عاجلة', label: 'عاجلة' },
    { value: 'عاجلة جداً', label: 'عاجلة جداً' }
  ];

  const statusOptions = [
    { value: 'قيد الإعداد', label: 'قيد الإعداد' },
    { value: 'جديدة', label: 'جديدة' },
    { value: 'قيد المراجعة', label: 'قيد المراجعة' },
    { value: 'مكتملة', label: 'مكتملة' }
  ];

  const deliveryMethodOptions = [
    { value: 'بريد إلكتروني', label: 'بريد إلكتروني' },
    { value: 'فاكس', label: 'فاكس' },
    { value: 'بريد عادي', label: 'بريد عادي' },
    { value: 'تسليم يدوي', label: 'تسليم يدوي' },
    { value: 'أخرى', label: 'أخرى' }
  ];

  const deliveryStatusOptions = [
    { value: 'قيد الإعداد', label: 'قيد الإعداد' },
    { value: 'تم الإرسال', label: 'تم الإرسال' },
    { value: 'تم التسليم', label: 'تم التسليم' },
    { value: 'فشل الإرسال', label: 'فشل الإرسال' }
  ];

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const newCorrespondence: OutgoingCorrespondence = {
        id: Date.now().toString(),
        type: 'صادرة',
        referenceNumber: CorrespondenceStorage.getNextReferenceNumber(),
        subject: formData.subject,
        content: formData.content,
        recipientName: formData.recipientName,
        recipientOrganization: formData.recipientOrganization,
        recipientEmail: formData.recipientEmail || undefined,
        recipientPhone: formData.recipientPhone || undefined,
        priority: formData.priority,
        status: formData.status,
        deliveryMethod: formData.deliveryMethod as any,
        deliveryStatus: formData.deliveryStatus as any,
        sentDate: formData.sentDate ? new Date(formData.sentDate) : undefined,
        sentBy: 'المستخدم الحالي', // يمكن تحديثه لاحقاً بنظام المستخدمين
        deliveryConfirmation: undefined,
        inResponseTo: formData.inResponseTo || undefined,
        createdAt: new Date(),
        updatedAt: new Date(),
        attachments: [],
        notes: formData.notes,
        tags: formData.tags ? formData.tags.split(',').map(tag => tag.trim()) : []
      };

      CorrespondenceStorage.saveOutgoingCorrespondence(newCorrespondence);
      router.push('/outgoing');
    } catch (error) {
      console.error('Error saving correspondence:', error);
      alert('حدث خطأ أثناء حفظ المراسلة');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      
      <main className="max-w-4xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {/* Header */}
          <div className="flex items-center gap-4 mb-6">
            <Button 
              variant="outline" 
              onClick={() => router.back()}
            >
              <ArrowRight className="h-4 w-4" />
            </Button>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">مراسلة صادرة جديدة</h1>
              <p className="text-gray-600 mt-1">
                إضافة مراسلة صادرة جديدة إلى النظام
              </p>
            </div>
          </div>

          <form onSubmit={handleSubmit}>
            <div className="space-y-6">
              {/* Basic Information */}
              <Card>
                <CardHeader>
                  <CardTitle>المعلومات الأساسية</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <Input
                    label="موضوع المراسلة *"
                    value={formData.subject}
                    onChange={(e) => handleInputChange('subject', e.target.value)}
                    required
                    placeholder="أدخل موضوع المراسلة"
                  />
                  
                  <Textarea
                    label="محتوى المراسلة *"
                    value={formData.content}
                    onChange={(e) => handleInputChange('content', e.target.value)}
                    required
                    placeholder="أدخل محتوى المراسلة"
                    rows={6}
                  />
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <Select
                      label="الأولوية"
                      value={formData.priority}
                      onChange={(e) => handleInputChange('priority', e.target.value)}
                      options={priorityOptions}
                    />
                    
                    <Select
                      label="الحالة"
                      value={formData.status}
                      onChange={(e) => handleInputChange('status', e.target.value)}
                      options={statusOptions}
                    />
                  </div>
                </CardContent>
              </Card>

              {/* Recipient Information */}
              <Card>
                <CardHeader>
                  <CardTitle>معلومات المستقبل</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <Input
                      label="اسم المستقبل *"
                      value={formData.recipientName}
                      onChange={(e) => handleInputChange('recipientName', e.target.value)}
                      required
                      placeholder="أدخل اسم المستقبل"
                    />
                    
                    <Input
                      label="الجهة المستقبلة *"
                      value={formData.recipientOrganization}
                      onChange={(e) => handleInputChange('recipientOrganization', e.target.value)}
                      required
                      placeholder="أدخل اسم الجهة المستقبلة"
                    />
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <Input
                      label="البريد الإلكتروني"
                      type="email"
                      value={formData.recipientEmail}
                      onChange={(e) => handleInputChange('recipientEmail', e.target.value)}
                      placeholder="<EMAIL>"
                    />
                    
                    <Input
                      label="رقم الهاتف"
                      value={formData.recipientPhone}
                      onChange={(e) => handleInputChange('recipientPhone', e.target.value)}
                      placeholder="05xxxxxxxx"
                    />
                  </div>
                </CardContent>
              </Card>

              {/* Delivery Information */}
              <Card>
                <CardHeader>
                  <CardTitle>معلومات التسليم</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <Select
                      label="طريقة التسليم"
                      value={formData.deliveryMethod}
                      onChange={(e) => handleInputChange('deliveryMethod', e.target.value)}
                      options={deliveryMethodOptions}
                    />
                    
                    <Select
                      label="حالة التسليم"
                      value={formData.deliveryStatus}
                      onChange={(e) => handleInputChange('deliveryStatus', e.target.value)}
                      options={deliveryStatusOptions}
                    />
                  </div>
                  
                  <Input
                    label="تاريخ الإرسال"
                    type="date"
                    value={formData.sentDate}
                    onChange={(e) => handleInputChange('sentDate', e.target.value)}
                  />
                </CardContent>
              </Card>

              {/* Additional Information */}
              <Card>
                <CardHeader>
                  <CardTitle>معلومات إضافية</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <Input
                    label="رداً على مراسلة (رقم المرجع)"
                    value={formData.inResponseTo}
                    onChange={(e) => handleInputChange('inResponseTo', e.target.value)}
                    placeholder="أدخل رقم المرجع للمراسلة المرد عليها"
                  />
                  
                  <Textarea
                    label="ملاحظات"
                    value={formData.notes}
                    onChange={(e) => handleInputChange('notes', e.target.value)}
                    placeholder="أدخل أي ملاحظات إضافية"
                    rows={3}
                  />
                  
                  <Input
                    label="العلامات (مفصولة بفواصل)"
                    value={formData.tags}
                    onChange={(e) => handleInputChange('tags', e.target.value)}
                    placeholder="مثال: مهم، عاجل، متابعة"
                  />
                </CardContent>
              </Card>

              {/* Action Buttons */}
              <div className="flex gap-4 justify-end">
                <Button 
                  type="button" 
                  variant="outline"
                  onClick={() => router.back()}
                >
                  <X className="h-4 w-4 ml-2" />
                  إلغاء
                </Button>
                <Button 
                  type="submit"
                  disabled={isSubmitting}
                >
                  <Save className="h-4 w-4 ml-2" />
                  {isSubmitting ? 'جاري الحفظ...' : 'حفظ المراسلة'}
                </Button>
              </div>
            </div>
          </form>
        </div>
      </main>
    </div>
  );
}
