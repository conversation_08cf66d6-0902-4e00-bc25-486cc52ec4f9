'use client';

import React from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { clsx } from 'clsx';
import { 
  Mail, 
  Send, 
  Search, 
  BarChart3, 
  Archive, 
  Home,
  FileText
} from 'lucide-react';

const navigationItems = [
  {
    name: 'الرئيسية',
    href: '/',
    icon: Home
  },
  {
    name: 'المراسلات الواردة',
    href: '/incoming',
    icon: Mail
  },
  {
    name: 'المراسلات الصادرة',
    href: '/outgoing',
    icon: Send
  },
  {
    name: 'البحث',
    href: '/search',
    icon: Search
  },
  {
    name: 'التقارير',
    href: '/reports',
    icon: BarChart3
  },
  {
    name: 'الأرشيف',
    href: '/archive',
    icon: Archive
  }
];

export const Navbar: React.FC = () => {
  const pathname = usePathname();

  return (
    <nav className="bg-white shadow-sm border-b">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          <div className="flex">
            <div className="flex-shrink-0 flex items-center">
              <FileText className="h-8 w-8 text-primary-600 ml-2" />
              <span className="text-xl font-bold text-gray-900">
                نظام إدارة المراسلات
              </span>
            </div>
          </div>
          
          <div className="hidden sm:ml-6 sm:flex sm:space-x-8 sm:space-x-reverse">
            {navigationItems.map((item) => {
              const Icon = item.icon;
              const isActive = pathname === item.href;
              
              return (
                <Link
                  key={item.name}
                  href={item.href}
                  className={clsx(
                    'inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium',
                    isActive
                      ? 'border-primary-500 text-primary-600'
                      : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700'
                  )}
                >
                  <Icon className="h-4 w-4 ml-2" />
                  {item.name}
                </Link>
              );
            })}
          </div>
        </div>
      </div>
      
      {/* Mobile menu */}
      <div className="sm:hidden">
        <div className="pt-2 pb-3 space-y-1">
          {navigationItems.map((item) => {
            const Icon = item.icon;
            const isActive = pathname === item.href;
            
            return (
              <Link
                key={item.name}
                href={item.href}
                className={clsx(
                  'block pl-3 pr-4 py-2 border-l-4 text-base font-medium',
                  isActive
                    ? 'bg-primary-50 border-primary-500 text-primary-700'
                    : 'border-transparent text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800'
                )}
              >
                <div className="flex items-center">
                  <Icon className="h-5 w-5 ml-2" />
                  {item.name}
                </div>
              </Link>
            );
          })}
        </div>
      </div>
    </nav>
  );
};
