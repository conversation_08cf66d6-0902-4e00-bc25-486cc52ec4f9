'use client';

import React, { useEffect, useState } from 'react';
import Link from 'next/link';
import { Navbar } from '@/components/layout/Navbar';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { CorrespondenceStorage } from '@/lib/data';
import { createSampleData, clearAllData } from '@/lib/sampleData';
import { CorrespondenceStats } from '@/types/correspondence';
import {
  Mail,
  Send,
  Clock,
  CheckCircle,
  AlertTriangle,
  Plus,
  TrendingUp,
  Database,
  Trash2
} from 'lucide-react';

export default function HomePage() {
  const [stats, setStats] = useState<CorrespondenceStats | null>(null);

  const loadStats = () => {
    // حساب الإحصائيات
    const incoming = CorrespondenceStorage.getIncomingCorrespondences();
    const outgoing = CorrespondenceStorage.getOutgoingCorrespondences();
    const all = [...incoming, ...outgoing];

    const statsData: CorrespondenceStats = {
      total: all.length,
      byStatus: {
        'جديدة': all.filter(c => c.status === 'جديدة').length,
        'قيد المراجعة': all.filter(c => c.status === 'قيد المراجعة').length,
        'مكتملة': all.filter(c => c.status === 'مكتملة').length,
        'مؤرشفة': all.filter(c => c.status === 'مؤرشفة').length,
        'ملغاة': all.filter(c => c.status === 'ملغاة').length,
      },
      byPriority: {
        'عادية': all.filter(c => c.priority === 'عادية').length,
        'مهمة': all.filter(c => c.priority === 'مهمة').length,
        'عاجلة': all.filter(c => c.priority === 'عاجلة').length,
        'عاجلة جداً': all.filter(c => c.priority === 'عاجلة جداً').length,
      },
      byType: {
        'واردة': incoming.length,
        'صادرة': outgoing.length,
      },
      monthlyStats: []
    };

    setStats(statsData);
  };

  useEffect(() => {
    loadStats();
  }, []);

  const handleCreateSampleData = () => {
    createSampleData();
    loadStats();
    alert('تم إنشاء البيانات التجريبية بنجاح!');
  };

  const handleClearData = () => {
    if (confirm('هل أنت متأكد من حذف جميع البيانات؟ هذا الإجراء لا يمكن التراجع عنه.')) {
      clearAllData();
      loadStats();
      alert('تم حذف جميع البيانات بنجاح!');
    }
  };

  const quickActions = [
    {
      title: 'مراسلة واردة جديدة',
      description: 'تسجيل مراسلة واردة جديدة',
      href: '/incoming/new',
      icon: Mail,
      color: 'bg-blue-500'
    },
    {
      title: 'مراسلة صادرة جديدة',
      description: 'إنشاء مراسلة صادرة جديدة',
      href: '/outgoing/new',
      icon: Send,
      color: 'bg-green-500'
    },
    {
      title: 'البحث في المراسلات',
      description: 'البحث والفلترة المتقدمة',
      href: '/search',
      icon: Clock,
      color: 'bg-purple-500'
    },
    {
      title: 'عرض التقارير',
      description: 'تقارير وإحصائيات شاملة',
      href: '/reports',
      icon: TrendingUp,
      color: 'bg-orange-500'
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              مرحباً بك في نظام إدارة المراسلات
            </h1>
            <p className="text-gray-600">
              نظام شامل لإدارة المراسلات الواردة والصادرة بكفاءة وسهولة
            </p>
          </div>

          {/* Statistics Cards */}
          {stats && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <Mail className="h-8 w-8 text-blue-600" />
                    </div>
                    <div className="mr-4">
                      <div className="text-2xl font-bold text-gray-900">
                        {stats.byType['واردة']}
                      </div>
                      <div className="text-sm text-gray-600">مراسلات واردة</div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <Send className="h-8 w-8 text-green-600" />
                    </div>
                    <div className="mr-4">
                      <div className="text-2xl font-bold text-gray-900">
                        {stats.byType['صادرة']}
                      </div>
                      <div className="text-sm text-gray-600">مراسلات صادرة</div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <Clock className="h-8 w-8 text-yellow-600" />
                    </div>
                    <div className="mr-4">
                      <div className="text-2xl font-bold text-gray-900">
                        {stats.byStatus['قيد المراجعة']}
                      </div>
                      <div className="text-sm text-gray-600">قيد المراجعة</div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <CheckCircle className="h-8 w-8 text-green-600" />
                    </div>
                    <div className="mr-4">
                      <div className="text-2xl font-bold text-gray-900">
                        {stats.byStatus['مكتملة']}
                      </div>
                      <div className="text-sm text-gray-600">مكتملة</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {/* Quick Actions */}
          <Card className="mb-8">
            <CardHeader>
              <CardTitle>الإجراءات السريعة</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {quickActions.map((action, index) => {
                  const Icon = action.icon;
                  return (
                    <Link key={index} href={action.href}>
                      <div className="p-4 border rounded-lg hover:shadow-md transition-shadow cursor-pointer">
                        <div className="flex items-center mb-3">
                          <div className={`p-2 rounded-lg ${action.color}`}>
                            <Icon className="h-6 w-6 text-white" />
                          </div>
                        </div>
                        <h3 className="font-semibold text-gray-900 mb-1">
                          {action.title}
                        </h3>
                        <p className="text-sm text-gray-600">
                          {action.description}
                        </p>
                      </div>
                    </Link>
                  );
                })}
              </div>
            </CardContent>
          </Card>

          {/* Recent Activity */}
          <Card>
            <CardHeader>
              <CardTitle>النشاط الأخير</CardTitle>
            </CardHeader>
            <CardContent>
              {stats && stats.total === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <AlertTriangle className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                  <p>لا توجد مراسلات حتى الآن</p>
                  <p className="text-sm mt-2">ابدأ بإضافة مراسلة جديدة أو استخدم البيانات التجريبية</p>
                  <div className="mt-4 space-y-2">
                    <div className="space-x-4 space-x-reverse">
                      <Link href="/incoming/new">
                        <Button>
                          <Plus className="h-4 w-4 ml-2" />
                          مراسلة واردة
                        </Button>
                      </Link>
                      <Link href="/outgoing/new">
                        <Button variant="outline">
                          <Plus className="h-4 w-4 ml-2" />
                          مراسلة صادرة
                        </Button>
                      </Link>
                    </div>
                    <div className="border-t pt-4 mt-4">
                      <p className="text-sm text-gray-600 mb-2">للاختبار والتجربة:</p>
                      <div className="space-x-2 space-x-reverse">
                        <Button variant="outline" onClick={handleCreateSampleData}>
                          <Database className="h-4 w-4 ml-2" />
                          إنشاء بيانات تجريبية
                        </Button>
                        <Button variant="outline" onClick={handleClearData}>
                          <Trash2 className="h-4 w-4 ml-2" />
                          مسح البيانات
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-center py-4">
                  <p className="text-gray-600 mb-4">يمكنك إدارة البيانات التجريبية:</p>
                  <div className="space-x-2 space-x-reverse">
                    <Button variant="outline" onClick={handleCreateSampleData}>
                      <Database className="h-4 w-4 ml-2" />
                      إضافة بيانات تجريبية
                    </Button>
                    <Button variant="outline" onClick={handleClearData}>
                      <Trash2 className="h-4 w-4 ml-2" />
                      مسح جميع البيانات
                    </Button>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  );
}
