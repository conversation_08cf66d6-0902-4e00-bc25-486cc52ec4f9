import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'نظام إدارة المراسلات',
  description: 'نظام شامل لإدارة المراسلات الواردة والصادرة',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="ar" dir="rtl">
      <body className={`${inter.className} rtl arabic-text`}>
        {children}
      </body>
    </html>
  )
}
