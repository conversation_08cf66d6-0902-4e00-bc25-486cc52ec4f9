'use client';

import React, { useEffect, useState } from 'react';
import { Navbar } from '@/components/layout/Navbar';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { CorrespondenceStorage } from '@/lib/data';
import { CorrespondenceStats, Correspondence } from '@/types/correspondence';
import { 
  BarChart3, 
  TrendingUp, 
  Download, 
  Calendar,
  Mail,
  Send,
  Clock,
  CheckCircle,
  AlertTriangle,
  Archive
} from 'lucide-react';
import { format, startOfMonth, endOfMonth, eachMonthOfInterval, subMonths } from 'date-fns';
import { ar } from 'date-fns/locale';

export default function ReportsPage() {
  const [stats, setStats] = useState<CorrespondenceStats | null>(null);
  const [monthlyData, setMonthlyData] = useState<any[]>([]);
  const [selectedPeriod, setSelectedPeriod] = useState('6months');

  useEffect(() => {
    generateReports();
  }, [selectedPeriod]);

  const generateReports = () => {
    const incoming = CorrespondenceStorage.getIncomingCorrespondences();
    const outgoing = CorrespondenceStorage.getOutgoingCorrespondences();
    const all = [...incoming, ...outgoing];

    // إحصائيات عامة
    const statsData: CorrespondenceStats = {
      total: all.length,
      byStatus: {
        'جديدة': all.filter(c => c.status === 'جديدة').length,
        'قيد المراجعة': all.filter(c => c.status === 'قيد المراجعة').length,
        'مكتملة': all.filter(c => c.status === 'مكتملة').length,
        'مؤرشفة': all.filter(c => c.status === 'مؤرشفة').length,
        'ملغاة': all.filter(c => c.status === 'ملغاة').length,
      },
      byPriority: {
        'عادية': all.filter(c => c.priority === 'عادية').length,
        'مهمة': all.filter(c => c.priority === 'مهمة').length,
        'عاجلة': all.filter(c => c.priority === 'عاجلة').length,
        'عاجلة جداً': all.filter(c => c.priority === 'عاجلة جداً').length,
      },
      byType: {
        'واردة': incoming.length,
        'صادرة': outgoing.length,
      },
      monthlyStats: []
    };

    // إحصائيات شهرية
    const months = selectedPeriod === '6months' ? 6 : 12;
    const startDate = startOfMonth(subMonths(new Date(), months - 1));
    const endDate = endOfMonth(new Date());
    
    const monthsInterval = eachMonthOfInterval({ start: startDate, end: endDate });
    
    const monthlyStats = monthsInterval.map(month => {
      const monthStart = startOfMonth(month);
      const monthEnd = endOfMonth(month);
      
      const monthIncoming = incoming.filter(c => 
        c.createdAt >= monthStart && c.createdAt <= monthEnd
      ).length;
      
      const monthOutgoing = outgoing.filter(c => 
        c.createdAt >= monthStart && c.createdAt <= monthEnd
      ).length;
      
      return {
        month: format(month, 'MMM yyyy', { locale: ar }),
        incoming: monthIncoming,
        outgoing: monthOutgoing,
        total: monthIncoming + monthOutgoing
      };
    });

    setStats(statsData);
    setMonthlyData(monthlyStats);
  };

  const exportReport = () => {
    if (!stats) return;
    
    const reportData = {
      تاريخ_التقرير: format(new Date(), 'dd/MM/yyyy', { locale: ar }),
      الإحصائيات_العامة: {
        إجمالي_المراسلات: stats.total,
        المراسلات_الواردة: stats.byType['واردة'],
        المراسلات_الصادرة: stats.byType['صادرة']
      },
      حسب_الحالة: stats.byStatus,
      حسب_الأولوية: stats.byPriority,
      الإحصائيات_الشهرية: monthlyData
    };
    
    const dataStr = JSON.stringify(reportData, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `تقرير_المراسلات_${format(new Date(), 'yyyy-MM-dd')}.json`;
    link.click();
  };

  if (!stats) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navbar />
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">جاري تحميل التقارير...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {/* Header */}
          <div className="flex justify-between items-center mb-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">التقارير والإحصائيات</h1>
              <p className="text-gray-600 mt-1">
                تقارير شاملة وإحصائيات مفصلة للمراسلات
              </p>
            </div>
            <div className="flex gap-4">
              <select
                value={selectedPeriod}
                onChange={(e) => setSelectedPeriod(e.target.value)}
                className="rounded-md border border-gray-300 px-3 py-2"
              >
                <option value="6months">آخر 6 أشهر</option>
                <option value="12months">آخر 12 شهر</option>
              </select>
              <Button onClick={exportReport}>
                <Download className="h-4 w-4 ml-2" />
                تصدير التقرير
              </Button>
            </div>
          </div>

          {/* Summary Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <BarChart3 className="h-8 w-8 text-blue-600" />
                  </div>
                  <div className="mr-4">
                    <div className="text-2xl font-bold text-gray-900">
                      {stats.total}
                    </div>
                    <div className="text-sm text-gray-600">إجمالي المراسلات</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <Mail className="h-8 w-8 text-green-600" />
                  </div>
                  <div className="mr-4">
                    <div className="text-2xl font-bold text-gray-900">
                      {stats.byType['واردة']}
                    </div>
                    <div className="text-sm text-gray-600">مراسلات واردة</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <Send className="h-8 w-8 text-purple-600" />
                  </div>
                  <div className="mr-4">
                    <div className="text-2xl font-bold text-gray-900">
                      {stats.byType['صادرة']}
                    </div>
                    <div className="text-sm text-gray-600">مراسلات صادرة</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <CheckCircle className="h-8 w-8 text-green-600" />
                  </div>
                  <div className="mr-4">
                    <div className="text-2xl font-bold text-gray-900">
                      {stats.byStatus['مكتملة']}
                    </div>
                    <div className="text-sm text-gray-600">مراسلات مكتملة</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Status Distribution */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
            <Card>
              <CardHeader>
                <CardTitle>توزيع المراسلات حسب الحالة</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {Object.entries(stats.byStatus).map(([status, count]) => {
                    const percentage = stats.total > 0 ? (count / stats.total * 100).toFixed(1) : 0;
                    const getStatusIcon = (status: string) => {
                      switch (status) {
                        case 'جديدة': return <Clock className="h-4 w-4 text-blue-600" />;
                        case 'قيد المراجعة': return <AlertTriangle className="h-4 w-4 text-yellow-600" />;
                        case 'مكتملة': return <CheckCircle className="h-4 w-4 text-green-600" />;
                        case 'مؤرشفة': return <Archive className="h-4 w-4 text-gray-600" />;
                        default: return <Clock className="h-4 w-4 text-gray-600" />;
                      }
                    };
                    
                    return (
                      <div key={status} className="flex items-center justify-between">
                        <div className="flex items-center">
                          {getStatusIcon(status)}
                          <span className="mr-2 text-sm font-medium">{status}</span>
                        </div>
                        <div className="flex items-center">
                          <div className="w-32 bg-gray-200 rounded-full h-2 ml-4">
                            <div 
                              className="bg-blue-600 h-2 rounded-full" 
                              style={{ width: `${percentage}%` }}
                            ></div>
                          </div>
                          <span className="text-sm text-gray-600 w-16 text-left">
                            {count} ({percentage}%)
                          </span>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>توزيع المراسلات حسب الأولوية</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {Object.entries(stats.byPriority).map(([priority, count]) => {
                    const percentage = stats.total > 0 ? (count / stats.total * 100).toFixed(1) : 0;
                    const getPriorityColor = (priority: string) => {
                      switch (priority) {
                        case 'عاجلة جداً': return 'bg-red-600';
                        case 'عاجلة': return 'bg-orange-600';
                        case 'مهمة': return 'bg-yellow-600';
                        case 'عادية': return 'bg-green-600';
                        default: return 'bg-gray-600';
                      }
                    };
                    
                    return (
                      <div key={priority} className="flex items-center justify-between">
                        <div className="flex items-center">
                          <div className={`w-4 h-4 rounded-full ml-2 ${getPriorityColor(priority)}`}></div>
                          <span className="text-sm font-medium">{priority}</span>
                        </div>
                        <div className="flex items-center">
                          <div className="w-32 bg-gray-200 rounded-full h-2 ml-4">
                            <div 
                              className={`h-2 rounded-full ${getPriorityColor(priority)}`}
                              style={{ width: `${percentage}%` }}
                            ></div>
                          </div>
                          <span className="text-sm text-gray-600 w-16 text-left">
                            {count} ({percentage}%)
                          </span>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Monthly Trends */}
          <Card>
            <CardHeader>
              <CardTitle>
                <div className="flex items-center">
                  <TrendingUp className="h-5 w-5 ml-2" />
                  الاتجاهات الشهرية
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent>
              {monthlyData.length > 0 ? (
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {monthlyData.map((month, index) => (
                      <div key={index} className="bg-gray-50 rounded-lg p-4">
                        <h4 className="font-semibold text-gray-900 mb-2">{month.month}</h4>
                        <div className="space-y-2 text-sm">
                          <div className="flex justify-between">
                            <span className="text-gray-600">واردة:</span>
                            <span className="font-medium text-blue-600">{month.incoming}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-600">صادرة:</span>
                            <span className="font-medium text-green-600">{month.outgoing}</span>
                          </div>
                          <div className="flex justify-between border-t pt-2">
                            <span className="text-gray-900 font-medium">المجموع:</span>
                            <span className="font-bold text-gray-900">{month.total}</span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <Calendar className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                  <p>لا توجد بيانات شهرية متاحة</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  );
}
