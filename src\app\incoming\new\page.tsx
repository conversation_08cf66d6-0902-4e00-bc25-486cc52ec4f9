'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Navbar } from '@/components/layout/Navbar';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Input, Textarea, Select } from '@/components/ui/Input';
import { CorrespondenceStorage } from '@/lib/data';
import { IncomingCorrespondence, Priority, CorrespondenceStatus } from '@/types/correspondence';
import { ArrowRight, Save, X } from 'lucide-react';

export default function NewIncomingCorrespondencePage() {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState({
    subject: '',
    content: '',
    senderName: '',
    senderOrganization: '',
    senderEmail: '',
    senderPhone: '',
    priority: 'عادية' as Priority,
    status: 'جديدة' as CorrespondenceStatus,
    responseRequired: false,
    responseDeadline: '',
    notes: '',
    tags: ''
  });

  const priorityOptions = [
    { value: 'عادية', label: 'عادية' },
    { value: 'مهمة', label: 'مهمة' },
    { value: 'عاجلة', label: 'عاجلة' },
    { value: 'عاجلة جداً', label: 'عاجلة جداً' }
  ];

  const statusOptions = [
    { value: 'جديدة', label: 'جديدة' },
    { value: 'قيد المراجعة', label: 'قيد المراجعة' },
    { value: 'مكتملة', label: 'مكتملة' }
  ];

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const newCorrespondence: IncomingCorrespondence = {
        id: Date.now().toString(),
        type: 'واردة',
        referenceNumber: CorrespondenceStorage.getNextReferenceNumber(),
        subject: formData.subject,
        content: formData.content,
        senderName: formData.senderName,
        senderOrganization: formData.senderOrganization,
        senderEmail: formData.senderEmail || undefined,
        senderPhone: formData.senderPhone || undefined,
        priority: formData.priority,
        status: formData.status,
        receivedDate: new Date(),
        receivedBy: 'المستخدم الحالي', // يمكن تحديثه لاحقاً بنظام المستخدمين
        responseRequired: formData.responseRequired,
        responseDeadline: formData.responseDeadline ? new Date(formData.responseDeadline) : undefined,
        responseStatus: formData.responseRequired ? 'لم يتم الرد' : undefined,
        createdAt: new Date(),
        updatedAt: new Date(),
        attachments: [],
        notes: formData.notes,
        tags: formData.tags ? formData.tags.split(',').map(tag => tag.trim()) : []
      };

      CorrespondenceStorage.saveIncomingCorrespondence(newCorrespondence);
      router.push('/incoming');
    } catch (error) {
      console.error('Error saving correspondence:', error);
      alert('حدث خطأ أثناء حفظ المراسلة');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      
      <main className="max-w-4xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {/* Header */}
          <div className="flex items-center gap-4 mb-6">
            <Button 
              variant="outline" 
              onClick={() => router.back()}
            >
              <ArrowRight className="h-4 w-4" />
            </Button>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">مراسلة واردة جديدة</h1>
              <p className="text-gray-600 mt-1">
                إضافة مراسلة واردة جديدة إلى النظام
              </p>
            </div>
          </div>

          <form onSubmit={handleSubmit}>
            <div className="space-y-6">
              {/* Basic Information */}
              <Card>
                <CardHeader>
                  <CardTitle>المعلومات الأساسية</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <Input
                    label="موضوع المراسلة *"
                    value={formData.subject}
                    onChange={(e) => handleInputChange('subject', e.target.value)}
                    required
                    placeholder="أدخل موضوع المراسلة"
                  />
                  
                  <Textarea
                    label="محتوى المراسلة *"
                    value={formData.content}
                    onChange={(e) => handleInputChange('content', e.target.value)}
                    required
                    placeholder="أدخل محتوى المراسلة"
                    rows={6}
                  />
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <Select
                      label="الأولوية"
                      value={formData.priority}
                      onChange={(e) => handleInputChange('priority', e.target.value)}
                      options={priorityOptions}
                    />
                    
                    <Select
                      label="الحالة"
                      value={formData.status}
                      onChange={(e) => handleInputChange('status', e.target.value)}
                      options={statusOptions}
                    />
                  </div>
                </CardContent>
              </Card>

              {/* Sender Information */}
              <Card>
                <CardHeader>
                  <CardTitle>معلومات المرسل</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <Input
                      label="اسم المرسل *"
                      value={formData.senderName}
                      onChange={(e) => handleInputChange('senderName', e.target.value)}
                      required
                      placeholder="أدخل اسم المرسل"
                    />
                    
                    <Input
                      label="الجهة المرسلة *"
                      value={formData.senderOrganization}
                      onChange={(e) => handleInputChange('senderOrganization', e.target.value)}
                      required
                      placeholder="أدخل اسم الجهة المرسلة"
                    />
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <Input
                      label="البريد الإلكتروني"
                      type="email"
                      value={formData.senderEmail}
                      onChange={(e) => handleInputChange('senderEmail', e.target.value)}
                      placeholder="<EMAIL>"
                    />
                    
                    <Input
                      label="رقم الهاتف"
                      value={formData.senderPhone}
                      onChange={(e) => handleInputChange('senderPhone', e.target.value)}
                      placeholder="05xxxxxxxx"
                    />
                  </div>
                </CardContent>
              </Card>

              {/* Response Information */}
              <Card>
                <CardHeader>
                  <CardTitle>معلومات الرد</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center space-x-2 space-x-reverse">
                    <input
                      type="checkbox"
                      id="responseRequired"
                      checked={formData.responseRequired}
                      onChange={(e) => handleInputChange('responseRequired', e.target.checked)}
                      className="rounded border-gray-300"
                    />
                    <label htmlFor="responseRequired" className="text-sm font-medium text-gray-700">
                      يتطلب رد
                    </label>
                  </div>
                  
                  {formData.responseRequired && (
                    <Input
                      label="موعد الرد المطلوب"
                      type="date"
                      value={formData.responseDeadline}
                      onChange={(e) => handleInputChange('responseDeadline', e.target.value)}
                    />
                  )}
                </CardContent>
              </Card>

              {/* Additional Information */}
              <Card>
                <CardHeader>
                  <CardTitle>معلومات إضافية</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <Textarea
                    label="ملاحظات"
                    value={formData.notes}
                    onChange={(e) => handleInputChange('notes', e.target.value)}
                    placeholder="أدخل أي ملاحظات إضافية"
                    rows={3}
                  />
                  
                  <Input
                    label="العلامات (مفصولة بفواصل)"
                    value={formData.tags}
                    onChange={(e) => handleInputChange('tags', e.target.value)}
                    placeholder="مثال: مهم، عاجل، متابعة"
                  />
                </CardContent>
              </Card>

              {/* Action Buttons */}
              <div className="flex gap-4 justify-end">
                <Button 
                  type="button" 
                  variant="outline"
                  onClick={() => router.back()}
                >
                  <X className="h-4 w-4 ml-2" />
                  إلغاء
                </Button>
                <Button 
                  type="submit"
                  disabled={isSubmitting}
                >
                  <Save className="h-4 w-4 ml-2" />
                  {isSubmitting ? 'جاري الحفظ...' : 'حفظ المراسلة'}
                </Button>
              </div>
            </div>
          </form>
        </div>
      </main>
    </div>
  );
}
