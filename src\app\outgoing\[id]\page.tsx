'use client';

import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { Navbar } from '@/components/layout/Navbar';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { CorrespondenceStorage } from '@/lib/data';
import { OutgoingCorrespondence } from '@/types/correspondence';
import { 
  ArrowRight, 
  Edit, 
  Archive, 
  Calendar,
  User,
  Building,
  Mail,
  Phone,
  Send,
  CheckCircle,
  AlertCircle,
  FileText,
  Tag,
  Truck
} from 'lucide-react';
import { format } from 'date-fns';
import { ar } from 'date-fns/locale';

interface PageProps {
  params: { id: string };
}

export default function OutgoingCorrespondenceDetailPage({ params }: PageProps) {
  const router = useRouter();
  const [correspondence, setCorrespondence] = useState<OutgoingCorrespondence | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadCorrespondence();
  }, [params.id]);

  const loadCorrespondence = () => {
    const data = CorrespondenceStorage.getCorrespondenceById(params.id);
    if (data && data.type === 'صادرة') {
      setCorrespondence(data as OutgoingCorrespondence);
    }
    setLoading(false);
  };

  const handleArchive = () => {
    if (!correspondence) return;
    
    if (confirm('هل أنت متأكد من أرشفة هذه المراسلة؟')) {
      const updatedCorrespondence = {
        ...correspondence,
        status: 'مؤرشفة' as any,
        updatedAt: new Date()
      };
      
      CorrespondenceStorage.saveOutgoingCorrespondence(updatedCorrespondence);
      router.push('/outgoing');
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'جديدة': return 'bg-blue-100 text-blue-800';
      case 'قيد المراجعة': return 'bg-yellow-100 text-yellow-800';
      case 'مكتملة': return 'bg-green-100 text-green-800';
      case 'مؤرشفة': return 'bg-gray-100 text-gray-800';
      case 'ملغاة': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getDeliveryStatusColor = (status: string) => {
    switch (status) {
      case 'قيد الإعداد': return 'bg-blue-100 text-blue-800';
      case 'تم الإرسال': return 'bg-yellow-100 text-yellow-800';
      case 'تم التسليم': return 'bg-green-100 text-green-800';
      case 'فشل الإرسال': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'عاجلة جداً': return 'bg-red-100 text-red-800';
      case 'عاجلة': return 'bg-orange-100 text-orange-800';
      case 'مهمة': return 'bg-yellow-100 text-yellow-800';
      case 'عادية': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navbar />
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">جاري تحميل المراسلة...</p>
          </div>
        </div>
      </div>
    );
  }

  if (!correspondence) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navbar />
        <div className="max-w-4xl mx-auto py-6 sm:px-6 lg:px-8">
          <div className="px-4 py-6 sm:px-0">
            <Card>
              <CardContent className="p-12 text-center">
                <AlertCircle className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  المراسلة غير موجودة
                </h3>
                <p className="text-gray-600 mb-4">
                  لم يتم العثور على المراسلة المطلوبة
                </p>
                <Link href="/outgoing">
                  <Button>
                    <ArrowRight className="h-4 w-4 ml-2" />
                    العودة للمراسلات الصادرة
                  </Button>
                </Link>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      
      <main className="max-w-4xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-4">
              <Link href="/outgoing">
                <Button variant="outline">
                  <ArrowRight className="h-4 w-4" />
                </Button>
              </Link>
              <div>
                <h1 className="text-3xl font-bold text-gray-900">تفاصيل المراسلة الصادرة</h1>
                <p className="text-gray-600 mt-1">
                  رقم المرجع: {correspondence.referenceNumber}
                </p>
              </div>
            </div>
            <div className="flex gap-2">
              <Link href={`/outgoing/${correspondence.id}/edit`}>
                <Button variant="outline">
                  <Edit className="h-4 w-4 ml-2" />
                  تعديل
                </Button>
              </Link>
              {correspondence.status !== 'مؤرشفة' && (
                <Button variant="outline" onClick={handleArchive}>
                  <Archive className="h-4 w-4 ml-2" />
                  أرشفة
                </Button>
              )}
            </div>
          </div>

          <div className="space-y-6">
            {/* Basic Information */}
            <Card>
              <CardHeader>
                <CardTitle>المعلومات الأساسية</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    {correspondence.subject}
                  </h3>
                  <div className="flex gap-2 mb-4">
                    <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(correspondence.status)}`}>
                      {correspondence.status}
                    </span>
                    <span className={`px-3 py-1 rounded-full text-sm font-medium ${getDeliveryStatusColor(correspondence.deliveryStatus)}`}>
                      {correspondence.deliveryStatus}
                    </span>
                    <span className={`px-3 py-1 rounded-full text-sm font-medium ${getPriorityColor(correspondence.priority)}`}>
                      {correspondence.priority}
                    </span>
                  </div>
                </div>
                
                <div className="bg-gray-50 rounded-lg p-4">
                  <h4 className="font-medium text-gray-900 mb-2">محتوى المراسلة:</h4>
                  <p className="text-gray-700 whitespace-pre-wrap">
                    {correspondence.content}
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Recipient Information */}
            <Card>
              <CardHeader>
                <CardTitle>معلومات المستقبل</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div className="flex items-center">
                      <User className="h-5 w-5 text-gray-400 ml-3" />
                      <div>
                        <p className="text-sm text-gray-600">اسم المستقبل</p>
                        <p className="font-medium">{correspondence.recipientName}</p>
                      </div>
                    </div>
                    
                    <div className="flex items-center">
                      <Building className="h-5 w-5 text-gray-400 ml-3" />
                      <div>
                        <p className="text-sm text-gray-600">الجهة المستقبلة</p>
                        <p className="font-medium">{correspondence.recipientOrganization}</p>
                      </div>
                    </div>
                  </div>
                  
                  <div className="space-y-4">
                    {correspondence.recipientEmail && (
                      <div className="flex items-center">
                        <Mail className="h-5 w-5 text-gray-400 ml-3" />
                        <div>
                          <p className="text-sm text-gray-600">البريد الإلكتروني</p>
                          <p className="font-medium">{correspondence.recipientEmail}</p>
                        </div>
                      </div>
                    )}
                    
                    {correspondence.recipientPhone && (
                      <div className="flex items-center">
                        <Phone className="h-5 w-5 text-gray-400 ml-3" />
                        <div>
                          <p className="text-sm text-gray-600">رقم الهاتف</p>
                          <p className="font-medium">{correspondence.recipientPhone}</p>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Delivery Information */}
            <Card>
              <CardHeader>
                <CardTitle>معلومات التسليم</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div className="flex items-center">
                      <Truck className="h-5 w-5 text-gray-400 ml-3" />
                      <div>
                        <p className="text-sm text-gray-600">طريقة التسليم</p>
                        <p className="font-medium">{correspondence.deliveryMethod}</p>
                      </div>
                    </div>
                    
                    <div className="flex items-center">
                      <User className="h-5 w-5 text-gray-400 ml-3" />
                      <div>
                        <p className="text-sm text-gray-600">مرسل بواسطة</p>
                        <p className="font-medium">{correspondence.sentBy}</p>
                      </div>
                    </div>
                  </div>
                  
                  <div className="space-y-4">
                    {correspondence.sentDate && (
                      <div className="flex items-center">
                        <Calendar className="h-5 w-5 text-gray-400 ml-3" />
                        <div>
                          <p className="text-sm text-gray-600">تاريخ الإرسال</p>
                          <p className="font-medium">
                            {format(correspondence.sentDate, 'dd/MM/yyyy', { locale: ar })}
                          </p>
                        </div>
                      </div>
                    )}
                    
                    {correspondence.deliveryConfirmation && (
                      <div className="flex items-center">
                        <CheckCircle className="h-5 w-5 text-green-500 ml-3" />
                        <div>
                          <p className="text-sm text-gray-600">تاريخ تأكيد التسليم</p>
                          <p className="font-medium">
                            {format(correspondence.deliveryConfirmation, 'dd/MM/yyyy', { locale: ar })}
                          </p>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Response Information */}
            {correspondence.inResponseTo && (
              <Card>
                <CardHeader>
                  <CardTitle>معلومات الرد</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center">
                    <Send className="h-5 w-5 text-gray-400 ml-3" />
                    <div>
                      <p className="text-sm text-gray-600">رداً على مراسلة</p>
                      <p className="font-medium">{correspondence.inResponseTo}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Additional Information */}
            {(correspondence.notes || correspondence.tags.length > 0) && (
              <Card>
                <CardHeader>
                  <CardTitle>معلومات إضافية</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {correspondence.notes && (
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2 flex items-center">
                        <FileText className="h-4 w-4 ml-2" />
                        ملاحظات
                      </h4>
                      <p className="text-gray-700 bg-gray-50 p-3 rounded-lg">
                        {correspondence.notes}
                      </p>
                    </div>
                  )}
                  
                  {correspondence.tags.length > 0 && (
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2 flex items-center">
                        <Tag className="h-4 w-4 ml-2" />
                        العلامات
                      </h4>
                      <div className="flex flex-wrap gap-2">
                        {correspondence.tags.map((tag, index) => (
                          <span
                            key={index}
                            className="px-2 py-1 bg-blue-100 text-blue-800 text-sm rounded-full"
                          >
                            {tag}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </main>
    </div>
  );
}
